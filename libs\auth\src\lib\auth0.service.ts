import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import fetch from "cross-fetch";
import { UpdateUserDto } from "./dto/update-user.dto";
import { CreateAuthCandidateDto } from "./dto/create-auth-candidate.dto";
import { CreateAuthCompanyDto } from "./dto/create-auth-company.dto";

interface user {
  email: string;
  password: string;
  user_metadata: any;
}

@Injectable()
export class Auth0Service {
  private readonly AUTH0_CLIENT_ID: string;
  private readonly AUTH0_CLIENT_SECRET: string;
  private readonly AUTH0_DOMAIN: string;
  private readonly AUTH0_AUDIENCE: string;
  private readonly maxRetries: number;
  private accessToken: string;
  private refreshToken: string;
  private tokenExpiryTime: number;
  private cache: { roles: any, cacheTTL:number }

  constructor() {
    this.AUTH0_DOMAIN = process.env.AUTH0_DOMAIN_MTM;
    this.AUTH0_CLIENT_ID = process.env.AUTH0_CLIENT_ID_MTM;
    this.AUTH0_CLIENT_SECRET = process.env.AUTH0_CLIENT_SECRET_MTM;
    this.AUTH0_AUDIENCE = process.env.AUTH0_AUDIENCE;
    this.maxRetries = 3;
    this.cache={
      roles:{},
      cacheTTL:3*60000
    }
    
  }

  // Set and Get methods for caching
  setCache(key, value, cacheType) {
    const expiry = Date.now() + this.cache.cacheTTL;
    this.cache[cacheType][key] = { value, expiry };
  }

  getCache(key, cacheType) {
    const cachedItem = this.cache[cacheType][key];
    if (cachedItem && Date.now() < cachedItem.expiry) {
      return cachedItem.value;
    }
    return null;
  }

  // async getToken () {
  //   const inputBody = `{"client_id":"${this.AUTH0_CLIENT_ID}","client_secret":"${this.AUTH0_CLIENT_SECRET}","audience":"${this.AUTH0_DOMAIN}/api/v2/","grant_type":"client_credentials"}`;
  //   const headers = {
  //     "Content-Type": "application/json",
  //   };
  //   let access_token;
  //   await fetch(`${this.AUTH0_DOMAIN}/oauth/token`, {
  //     method: "POST",
  //     body: inputBody,
  //     headers: headers,
  //   })
  //     .then(function (res) {
  //       return res.json();
  //     })
  //     .then(function (body: any) {
  //       access_token = body.access_token;
  //     });

  //   return access_token;
  // }

  async getToken() {
    if (!this.accessToken || Date.now() >= this.tokenExpiryTime) {
      await this.requestNewToken();
    }
    return this.accessToken;
  }

  private async requestNewToken() {
    const inputBody = JSON.stringify({
      client_id: this.AUTH0_CLIENT_ID,
      client_secret: this.AUTH0_CLIENT_SECRET,
      audience: `${this.AUTH0_DOMAIN}/api/v2/`,
      grant_type: "client_credentials",
    });

    const headers = {
      "Content-Type": "application/json",
    };

    const response = await fetch(`${this.AUTH0_DOMAIN}/oauth/token`, {
      method: "POST",
      body: inputBody,
      headers: headers,
    });

    const body = await response.json();

    this.accessToken = body.access_token;
    this.refreshToken = body.refresh_token;
    this.tokenExpiryTime = Date.now() + body.expires_in * 1000;

    // Set up auto-refresh for every 5 minutes
    setTimeout(() => {
      this.refreshAccessToken();
    }, 5 * 60 * 1000); // 5 minutes in milliseconds
  }

  private async refreshAccessToken() {
    const inputBody = JSON.stringify({
      client_id: this.AUTH0_CLIENT_ID,
      client_secret: this.AUTH0_CLIENT_SECRET,
      refresh_token: this.refreshToken,
      grant_type: "refresh_token",
    });

    const headers = {
      "Content-Type": "application/json",
    };

    const response = await fetch(`${this.AUTH0_DOMAIN}/oauth/token`, {
      method: "POST",
      body: inputBody,
      headers: headers,
    });

    const body = await response.json();

    if (body.access_token) {
      this.accessToken = body.access_token;
      this.tokenExpiryTime = Date.now() + body.expires_in * 1000;

      // Reset the auto-refresh timer
      setTimeout(() => {
        this.refreshAccessToken();
      }, 2 * 60 * 1000);
    } else {
      // Handle error
      console.error('Failed to refresh token:', body);
    }
  }

  // async getRefreshToken () {
  //   const inputBody = `{"client_id":"${this.AUTH0_CLIENT_ID}","client_secret":"${this.AUTH0_CLIENT_SECRET}","audience":"${this.AUTH0_DOMAIN}/api/v2/","grant_type":"client_credentials","scope":"offline_access"}`;
  //   const headers = {
    
  //    "Content-Type": "application/json",
  //   };
  //   let access_token;
  //   await fetch(`${this.AUTH0_DOMAIN}/oauth/token`, {
  //     method: "POST",
  //     body: inputBody,
  //     headers: headers,
  //   })
  //     .then(function (res) {
  //       return res.json();
  //     })
  //     .then(function (body: any) {
  //       access_token = body.access_token;
  //     });

  //   return access_token;
  // }

  async createCandidate (dto: CreateAuthCandidateDto) {
    const data = {
      email: dto.email,
      password: dto.password,
      connection: "Username-Password-Authentication",
      user_metadata: {
        firstname: dto.firstname,
        middlename: dto.middlename,
        lastname: dto.lastname,
        phone: dto.phone,
        userId: dto.userId,
        candidateId: dto.candidateId,
        recruiterId: "",
        companyId: "",
        tenantId: "app",
      },
      // username: dto.email
    };
    const user = await this.createUserWithData(data);
    await this.assignRoles(user.user_id, ["rol_knAZ2LRaIxsg7Uth"]); // Get from the env
    console.log(user)
    return user;
  }

  async assignRoles(id: string, roles: string[]) {
    const token = await this.getToken();
    const data = {
      roles: roles,
    };
    return await fetch(`${this.AUTH0_DOMAIN}/api/v2/users/${id}/roles`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async createCompanyMember(dto: CreateAuthCompanyDto) {
    const data = {
      email: dto.email,
      password: dto.password,
      connection: 'Username-Password-Authentication',
      user_metadata: {
        firstname: dto.firstname,
        middlename: dto.middlename,
        lastname: dto.lastname,
        phone: dto.phone,
        userId: dto.userId,
        candidateId: "",
        recruiterId: dto.recruiterId,
        companyId: dto.companyId,
        tenantId: dto.tenantId,
      },
      // username: dto.email
    };
    return await this.createUserWithData(data);
  }

  async createUserWithData (data: user) {
    let res;
    const token = await this.getToken();
    await fetch(`${this.AUTH0_DOMAIN}/api/v2/users`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "POST",
      body: JSON.stringify(data),
    })
      .then(function (res) {
        return res.json();
      })
      .then(function (body) {
        if (body.error) {
          throw new HttpException(body.message, HttpStatus.CONFLICT);
        } else {
          res = body;
        }
      });
    return res;
  }

  async updateUser (dto: UpdateUserDto) {
    const token = await this.getToken();
    let res;
    const data = {
      connection: "Username-Password-Authentication",
      user_metadata: {
        firstname: dto.firstname,
        middlename: dto.middlename,
        lastname: dto.lastname,
        phone: dto.phone,
        companyId: dto.companyId
      },
    };
    await fetch(`${this.AUTH0_DOMAIN}/api/v2/users/${dto.authId}`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "PATCH",
      body: JSON.stringify(data),
    })
      .then(function (res) {
        return res.json();
      })
      .then(function (body) {
        if (body.error) {
          throw new HttpException(body.message, HttpStatus.BAD_REQUEST);
        } else {
          res = body;
        }
      });
    return res;
  }

  async changePassword (password: string, id: string) {
    const token = await this.getToken();
    let res;
    const data = {
      connection: "Username-Password-Authentication",
      password: password,
    };
    await fetch(`${this.AUTH0_DOMAIN}/api/v2/users/${id}`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "PATCH",
      body: JSON.stringify(data),
    })
      .then(function (res) {
        return res.json();
      })
      .then(function (body) {
        if (body.error) {
          throw new HttpException(body.message, HttpStatus.BAD_REQUEST);
        } else {
          res = body;
        }
      });
    return res;
  }

  async removeRoles (id: string, roles: string[]) {
    const token = await this.getToken();
    const data = {
      roles: roles,
    };
    return await fetch(`${this.AUTH0_DOMAIN}/api/v2/users/${id}/roles`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "DELETE",
      body: JSON.stringify(data),
    });
  }

  async getAllRoles(filterName='') {
    const token = await this.getToken();
    const cacheRole = filterName && this.getCache(`${filterName}`, 'roles')
    if(cacheRole){
      return cacheRole
    }

    let apiStr = `${this.AUTH0_DOMAIN}/api/v2/roles`
    if (filterName) {
      apiStr += `?name_filter=${filterName}`
    }

    const response = await fetch(apiStr, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get all roles: ${response.statusText}`);
    }

    return response.json();
  }

  async retryRequest(fn, retries = this.maxRetries, delay = 1000) {
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        if ((error.message.includes("502") || error.statusCode === 429) && attempt < retries - 1) {
          console.warn(`Retrying request due to ${error.statusCode===429 ? "Too many requests":" Bad gateway "} error... Attempt ${attempt + 1}`);
          await this.delay(delay);
          delay *= 1.2;  // Exponential backoff
        }
        else {
          throw error;
        }
      }
    }
  }

  async createCompanyRole(name, description, token = null) {
    const bearerToken = token || await this.getToken();
    const existingRole = await this.getAllRoles(name);
    if (existingRole?.length && existingRole[0]?.name === name) {
      return {existingRole};
    }
    const createRoleRequest = () => fetch(`${this.AUTH0_DOMAIN}/api/v2/roles`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${bearerToken}`,
      },
      method: "POST",
      body: JSON.stringify({ name, description }),
    });

    const response = await this.retryRequest(createRoleRequest);
    if (!response.ok) {
      throw new Error(`Failed to create role: ${response.statusText}`);
    }
    return {newRole:await response.json()};
  }

  async deleteCompanyRole(id){
    const bearerToken = await this.getToken();

    const deleteRoleRequest = () => fetch(`${this.AUTH0_DOMAIN}/api/v2/roles/${id}`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${bearerToken}`,
      },
      method: "DELETE",
    });
    const response = await this.retryRequest(deleteRoleRequest);
    if (!response.ok) {
      throw new Error(`Failed to delete role: ${response.statusText}`);
    }
    return true;
  }

  async setDefaultPermissionsToCompanyRole(role, tenantId) {
    const token = await this.getToken();

    try {
      const cacheRole = this.getCache(`${role.label}`, 'roles')
      const {existingRole,newRole} = await this.createCompanyRole(`${role.label}_${tenantId}`, `${role.description} for ${tenantId}`, token);

      if(existingRole){
        return;
      }
      if (!newRole) {
        throw new Error("Failed to create new role.");
      }
      const permissions = cacheRole?.permissions?.length ? cacheRole?.permissions : await this.getPermissionsByRoleId(role.value, token);
      if (!permissions?.length) {
        return;
      }
      !cacheRole && this.setCache(`${role.label}`, { ...(role?.dataValues || role), permissions }, 'roles')

      const permissionsToAdd = permissions.map(i => i["permission_name"]);
      const addPermissionRequest = () => this.addPermissionsByRole(newRole.id, permissionsToAdd, token)
      const data = await this.retryRequest(addPermissionRequest);
      if (data) {
        this.setCache(`${role.label}_${tenantId}`, { permissions: permissionsToAdd, ...(newRole?.dataValues || newRole) }, 'roles')
      }
    } catch (error) {
      Logger.log("Error assigning permission to company role:", error);
    }
  }

  async getPermissionsByRoleId(roleId, token = null) {
    if (roleId) {
      const bearerToken = token || await this.getToken();
      const response = await fetch(`${this.AUTH0_DOMAIN}/api/v2/roles/${roleId}/permissions?per_page=100`, {
        headers: {
          "content-type": "application/json",
          Authorization: `Bearer ${bearerToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get permissions for role ${roleId}: ${response.statusText}`);
      }

      return response.json();
    }
  }

  async addPermissionsByRole(roleId, permissions, token = null) {
    const bearerToken = token || await this.getToken();
    const isRoleExist = await this.getPermissionsByRoleId(roleId);

    if (isRoleExist) {
      const permissionsArr = permissions.map(permission => ({
        resource_server_identifier: `${this.AUTH0_AUDIENCE}`,
        permission_name: `${permission}`,
      }));

      const response = await fetch(`${this.AUTH0_DOMAIN}/api/v2/roles/${roleId}/permissions`, {
        headers: {
          "content-type": "application/json",
          Authorization: `Bearer ${bearerToken}`,
        },
        method: "POST",
        body: JSON.stringify({ permissions: permissionsArr }),
      });
      if (!response.ok) {
        throw new Error(`Failed to assign permissions to role ${roleId}: ${response.statusText}`);
      }
      return response.json();
    }
  }

  async removePermissionsByRole(roleId, permissions: any[]) {
    const token = await this.getToken();
    let permissionsArr = []
    for (let permission of permissions) {
      permissionsArr.push({
        resource_server_identifier: `${this.AUTH0_AUDIENCE}`,
        permission_name: `${permission}`,
      })
    }
    const response = await fetch(`${this.AUTH0_DOMAIN}/api/v2/roles/${roleId}/permissions`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "DELETE",
      body: JSON.stringify({ "permissions": permissionsArr }),
    });

    if (!response.ok) {
      throw new Error(`Failed to remove permissions from role ${roleId}: ${response.statusText}`);
    }
    return true
  }

  async deleteUser(id: string) {
    const token = await this.getToken();
    const data = {
      connection: "Username-Password-Authentication",
    };
    let status;
    await fetch(`${this.AUTH0_DOMAIN}/api/v2/users/${id}`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "DELETE",
      body: JSON.stringify(data),
    }).then(function (res) {
      status = res.status;
    });
    return status === 204;
  }

  async isExistUser(email: string) {
    const token = await this.getToken();
    const user = await fetch(`${this.AUTH0_DOMAIN}/api/v2/users-by-email?fields=email&email=${email}`, {
      headers: {
        "content-type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      method: "GET",
    }).then((response) => {
      return response.json();
    });
    if (user[0]) {
      throw new HttpException("Email already exists", HttpStatus.CONFLICT);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
