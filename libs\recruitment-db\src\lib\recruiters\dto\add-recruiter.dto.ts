import { ApiProperty, IntersectionType } from "@nestjs/swagger";
import { DefaultUserDto } from "../../users/dto/default-user.dto";
import { ProfileRecruiterDto } from "./profile-recruiter.dto";

const RoleExample = [{ id: 1, value: "rol_XSvI7wOambKTG5L2"}]; // Get from the env

export class AddRecruiterDto extends IntersectionType(DefaultUserDto, ProfileRecruiterDto) {
  @ApiProperty({ example: RoleExample, description: "Roles Array" })
  roles: typeof RoleExample;

  @ApiProperty({ example: "ecdev", description: "tenant ID" })
  tenantId: string;
}
