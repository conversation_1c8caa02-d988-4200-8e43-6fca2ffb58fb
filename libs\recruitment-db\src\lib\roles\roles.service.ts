import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Role } from "./roles.model";
import { RoleDto } from "./dto/role.dto";
import * as data from "./data.json";
import { Op } from "sequelize";
import { Auth0Service } from "@microservices/auth";

@Injectable()
export class RolesService {
  constructor (
    @InjectModel(Role) private roleRepository: typeof Role,
    private auth0Service : Auth0Service
  ) {}

  async getRole (dto: RoleDto) {
    const [role] = await this.roleRepository.findOrCreate({
      where: {
        value: dto.value,
        label: dto.label,
      },
    });
    return role;
  }

  async getRoleByLabel(label,companyId=null){
    return await this.roleRepository.findOne({where:{
      label: {
        [Op.iLike] :`%${label}%`
      },
      ...(companyId ? {companyId}:'')
    }})
  }

  async seed () {
    await this.roleRepository.bulkCreate(data, {
      validate: true,
    })
      .then(async () => this.roleRepository.sequelize.query(
        `ALTER SEQUENCE "${this.roleRepository.tableName}_id_seq" RESTART WITH ${await this.roleRepository.count() + 1}`));
    return true;
  }

  async getAIM (companyId) {
    return await this.roleRepository.findAll({
      where: {
        value: { [Op.notLike]: "%rol_knAZ2LRaIxsg7Uth" },  // Get from the env
        companyId: companyId
      },
      order: [["id", "DESC"]],
    });
  }

  async getDefaultCompanyRoles () {
    return await this.roleRepository.findAll({
      attributes: ['id', 'label', 'value','description'],
      where: {
        [Op.and]: [{ value: { [Op.notLike]: "%rol_knAZ2LRaIxsg7Uth" } },{companyId:{[Op.is]:null}}],  // Get from the env
      },
      order: [["id", "asc"]],
    });
  }

  async getAvailableCompanyRoles (companyId: number) {
    return await this.roleRepository.findAll({
      attributes: ['id', 'label', 'value','description'],
      where: {
        [Op.and]: [{ value: { [Op.notLike]: "%rol_knAZ2LRaIxsg7Uth" } },{companyId}], // Get from the env
      },
      order: [["id", "asc"]],
    });
  }

  async createDefaultCompanyRole(roleName,tenantId,companyId){
    try{
      let role = await this.auth0Service.getAllRoles(roleName+"_"+tenantId)
      if(role){
        if(!Array.isArray(role)){
          role = [role]
        }
        const defaultRole = role.find(r=>r.name === roleName+"_"+tenantId)
        if(defaultRole){
          const rolePermissions = await this.getDefaultCompanyRole(roleName)
          return await this.roleRepository.create({companyId,value:defaultRole.id,label:defaultRole.name,description:defaultRole.description,permissions:rolePermissions.permissions});
        }
      }
    }catch(error){
      throw new Error(`Error while creating default company roles: ${error}`)
    }
  }

  async updateRolePermissions(roleId,permissions){
    return await this.roleRepository.update({permissions},{
      where:{
        value:roleId
      }
    })
  }
  async deleteAllCompanyRoles(companyId){
    return await this.roleRepository.destroy({where:{companyId}})
  }

  async getCompanyRoles(companyId){
    return await this.roleRepository.findAndCountAll({where:{companyId}})

  }

  async getDefaultCompanyRole(roleName:string){
    const data =  await this.roleRepository.findOne({where:{
      label:{
        [Op.like]:`${roleName}`,
      },
      companyId:{
        [Op.eq]:null
      },
    }})
    return data;
  }


}
